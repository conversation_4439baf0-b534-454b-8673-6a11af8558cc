# contextual_notes

A new Flutter project.

## Getting Started

This project is a starting point for a Flutter application.

A few resources to get you started if this is your first Flutter project:

- [Lab: Write your first Flutter app](https://docs.flutter.dev/get-started/codelab)
- [Cookbook: Useful Flutter samples](https://docs.flutter.dev/cookbook)

For help getting started with Flutter development, view the
[online documentation](https://docs.flutter.dev/), which offers tutorials,
samples, guidance on mobile development, and a full API reference.
# Contextual Notes

## Konfiguracja API Pogodowego

Aplikacja korzysta z Open-Meteo API do pobierania danych pogodowych. Jest to darmowe API, które nie wymaga klucza API dla użytku niekomercyjnego.

Zobacz dokumentację: [Open-Meteo API](https://open-meteo.com/en/docs)

## Funkcje pogodowe

System pobiera następujące dane pogodowe podczas zapisywania notatki:

- Temperatura (w °C)
- <PERSON>ady (w mm)
- <PERSON><PERSON><PERSON><PERSON>nie (w hPa)
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (w %)
- <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> wiatru (w m/s)
- Ogólne warunki pogodowe (np. słonecznie, deszczowo, pochmurno)

Dane te są zapisywane wraz z notatką i wyświetlane w widoku szczegółów.
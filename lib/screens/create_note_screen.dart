import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/note_provider.dart';
import 'package:geolocator/geolocator.dart';
import '../models/note.dart';
import 'package:flutter_speech/flutter_speech.dart';

class CreateNoteScreen extends StatefulWidget {
  const CreateNoteScreen({super.key});

  @override
  State<CreateNoteScreen> createState() => _CreateNoteScreenState();
}

class _CreateNoteScreenState extends State<CreateNoteScreen> {
  final _titleController = TextEditingController();
  final _contentController = TextEditingController();
  final _formKey = GlobalKey<FormState>();
  bool _isLoading = false;
  String? _permissionStatus;
  String? _contextError;
  Position? _lastPosition;
  String? _locationError;
  NoteContext? _lastCollectedContext;
  late SpeechRecognition _speech;
  bool _isListening = false;
  bool _speechRecognitionAvailable = false;
  String _currentLocale = 'pl_PL';
  String transcription = '';
  String _previousTranscription = '';

  @override
  void initState() {
    super.initState();
    _checkLocationPermission();
    _tryGetLocation();
    _speech = SpeechRecognition();
    _speech.setAvailabilityHandler((bool result) => setState(() => _speechRecognitionAvailable = result));
    _speech.setRecognitionStartedHandler(() => setState(() {
      _isListening = true;
      // Zapisujemy aktualny tekst przy rozpoczęciu dyktowania
      _previousTranscription = _contentController.text;
    }));
    _speech.setRecognitionResultHandler((String text) => setState(() {
      transcription = text;
      // Ustawiamy tekst jako połączenie poprzedniego tekstu i nowego rozpoznanego tekstu
      _contentController.text = _previousTranscription.isEmpty ? text : _previousTranscription + ' ' + text;
      _contentController.selection = TextSelection.fromPosition(
        TextPosition(offset: _contentController.text.length),
      );
    }));
    _speech.setRecognitionCompleteHandler((String text) {
      setState(() {
        _isListening = false;
        // Nie aktualizujemy _previousTranscription, bo zostało to już zrobione
        // przy rozpoczęciu dyktowania
      });
    });
    _speech.activate(_currentLocale).then((res) => setState(() => _speechRecognitionAvailable = res));
  }

  @override
  void dispose() {
    _titleController.dispose();
    _contentController.dispose();
    super.dispose();
  }

  Future<void> _checkLocationPermission() async {
    try {
      LocationPermission permission = await Geolocator.checkPermission();
      setState(() {
        _permissionStatus = permission.toString();
      });
    } catch (e) {
      setState(() {
        _permissionStatus = 'Error: $e';
      });
    }
  }

  Future<void> _tryGetLocation() async {
    try {
      bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
      if (!serviceEnabled) {
        setState(() {
          _locationError = 'Location services are disabled.';
        });
        return;
      }
      LocationPermission permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied || permission == LocationPermission.deniedForever) {
        setState(() {
          _locationError = 'Location permission denied.';
        });
        return;
      }
      Position pos = await Geolocator.getCurrentPosition(desiredAccuracy: LocationAccuracy.high, timeLimit: const Duration(seconds: 10));
      setState(() {
        _lastPosition = pos;
        _locationError = null;
      });
    } catch (e) {
      setState(() {
        _locationError = 'Location error: $e';
      });
    }
  }

  Future<void> _saveNote() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() {
      _isLoading = true;
      _contextError = null;
      _lastCollectedContext = null;
    });

    try {
      final noteProvider = Provider.of<NoteProvider>(context, listen: false);
      // Collect context directly here for debug
      final collectedContext = await noteProvider.collectContextForDebug();
      setState(() {
        _lastCollectedContext = collectedContext;
      });
      // Use date/time as title if empty
      String title = _titleController.text.trim();
      if (title.isEmpty) {
        final now = DateTime.now();
        title = 'Note ${now.year}-${now.month.toString().padLeft(2, '0')}-${now.day.toString().padLeft(2, '0')} ${now.hour.toString().padLeft(2, '0')}:${now.minute.toString().padLeft(2, '0')}';
      }
      await noteProvider.addNote(
        title,
        _contentController.text.trim(),
      );
      if (mounted) {
        Navigator.pop(context);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Note created successfully!'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _contextError = e.toString();
        });
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error creating note: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _onSpeechToText() async {
    if (!_isListening && _speechRecognitionAvailable) {
      // Zapisz aktualny tekst przed rozpoczęciem nowego dyktowania
      setState(() {
        _previousTranscription = _contentController.text;
      });
      _speech.listen().then((result) => print('result : $result'));
    } else if (_isListening) {
      _speech.stop();
      setState(() => _isListening = false);
    }
  }

  void _clearTranscription() {
    setState(() {
      _previousTranscription = '';
      transcription = '';
      _contentController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Note'),
        backgroundColor: Theme.of(context).colorScheme.inversePrimary,
        actions: [
          if (_isLoading)
            const Padding(
              padding: EdgeInsets.all(16.0),
              child: SizedBox(
                width: 20,
                height: 20,
                child: CircularProgressIndicator(strokeWidth: 2),
              ),
            )
          else
            IconButton(
              onPressed: _saveNote,
              icon: const Icon(Icons.save),
              tooltip: 'Save Note',
            ),
        ],
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Form(
          key: _formKey,
          child: SingleChildScrollView(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Status icons row
                Row(
                  children: [
                    Tooltip(
                      message: (_lastPosition != null && _locationError == null)
                          ? 'Location available'
                          : 'Location unavailable',
                      child: Icon(
                        Icons.pin_drop,
                        color: (_lastPosition != null && _locationError == null)
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Tooltip(
                      message: (_lastCollectedContext != null &&
                              _lastCollectedContext!.batteryLevel != null &&
                              _lastCollectedContext!.connectionType != null)
                          ? 'Device info available'
                          : 'Device info unavailable',
                      child: Icon(
                        Icons.phone_android,
                        color: (_lastCollectedContext != null &&
                                _lastCollectedContext!.batteryLevel != null &&
                                _lastCollectedContext!.connectionType != null)
                            ? Colors.green
                            : Colors.red,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                // Title field
                TextFormField(
                  controller: _titleController,
                  decoration: const InputDecoration(
                    labelText: 'Title',
                    hintText: 'Enter note title (optional)',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.title),
                  ),
                  // Title is now optional, so no validator needed
                ),
                const SizedBox(height: 16),
                // Content field
                SizedBox(
                  height: 200,
                  child: TextFormField(
                    controller: _contentController,
                    decoration: const InputDecoration(
                      labelText: 'Content',
                      hintText: 'Write your note here...',
                      border: OutlineInputBorder(),
                      alignLabelWithHint: true,
                    ),
                    maxLines: null,
                    expands: true,
                    textAlignVertical: TextAlignVertical.top,
                    validator: (value) {
                      if (value == null || value.trim().isEmpty) {
                        return 'Please enter note content';
                      }
                      return null;
                    },
                  ),
                ),
                // Removed locale dropdown as flutter_speech does not support listing locales
                const SizedBox(height: 8),
              ],
            ),
          ),
        ),
      ),
      bottomNavigationBar: SafeArea(
        bottom: true,
        child: Container(
          padding: const EdgeInsets.fromLTRB(16, 12, 16, 24),
          color: Theme.of(context).scaffoldBackgroundColor,
          child: Row(
            children: [
              ElevatedButton.icon(
                onPressed: _isLoading ? null : _onSpeechToText,
                icon: Icon(_isListening ? Icons.mic_off : Icons.mic),
                label: Text(_isListening ? 'Stop' : 'Dictate'),
                style: ElevatedButton.styleFrom(
                  padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
                ),
              ),
              const SizedBox(width: 8),
              IconButton(
                onPressed: _isLoading ? null : _clearTranscription,
                icon: const Icon(Icons.clear),
                tooltip: 'Clear text',
                style: IconButton.styleFrom(
                  backgroundColor: Colors.grey[200],
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isLoading ? null : _saveNote,
                  icon: _isLoading
                      ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Icon(Icons.save),
                  label: Text(_isLoading ? 'Saving...' : 'Save Note'),
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 12),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
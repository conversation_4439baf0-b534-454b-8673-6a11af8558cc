import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/note_provider.dart';
import '../models/note.dart';

class NoteDetailScreen extends StatelessWidget {
  final dynamic note;

  const NoteDetailScreen({Key? key, required this.note}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(note.title ?? 'Note Detail'),
      ),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                note.title ?? '',
                // style: Theme.of(context).textTheme.headlineSmall,
              ),
              // Dodanie sekcji z pogodą, jeśli dostępne
              if (note.context?.weatherCondition != null)
                _buildWeatherCard(context, note.context!),
              const SizedBox(height: 16),
              Text(
                note.content ?? '',
                style: Theme.of(context).textTheme.bodyLarge,
              ),
              const SizedBox(height: 24),
              if (note.context != null)
                Container(
                  padding: const EdgeInsets.all(12.0),
                  decoration: BoxDecoration(
                    color: Theme.of(context).colorScheme.surfaceContainerHighest,
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Consumer<NoteProvider>(
                    builder: (context, noteProvider, _) {
                      final items = noteProvider.getContextSummaryItems(note.context, context);
                      return Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          for (final item in items)
                            GestureDetector(
                              onTap: item.onTap,
                              child: Padding(
                                padding: const EdgeInsets.symmetric(vertical: 2.0),
                                child: Row(
                                  children: [
                                    Icon(item.icon, size: 16, color: Theme.of(context).colorScheme.onSurfaceVariant),
                                    const SizedBox(width: 8),
                                    Expanded(
                                      child: Text(
                                        item.text,
                                        style: TextStyle(
                                          fontSize: 13,
                                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                                          decoration: item.onTap != null ? TextDecoration.underline : null,
                                        ),
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                        ],
                      );
                    },
                  ),
                ),
              const SizedBox(height: 24),
              Text(
                _formatFullDateTime(note.createdAt),
                style: TextStyle(fontSize: 12, color: Colors.grey[600]),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildWeatherCard(BuildContext context, NoteContext noteContext) {
    // Wybierz odpowiednią ikonę pogodową
    IconData weatherIcon;
    switch (noteContext.weatherCondition?.toLowerCase()) {
      case 'clear':
        weatherIcon = Icons.wb_sunny;
        break;
      case 'partly cloudy':
        weatherIcon = Icons.cloud_queue;
        break;
      case 'cloudy':
        weatherIcon = Icons.cloud;
        break;
      case 'rain':
      case 'drizzle':
        weatherIcon = Icons.water_drop;
        break;
      case 'freezing drizzle':
      case 'freezing rain':
        weatherIcon = Icons.ac_unit;
        break;
      case 'thunderstorm':
        weatherIcon = Icons.flash_on;
        break;
      case 'snow':
        weatherIcon = Icons.ac_unit;
        break;
      case 'mist':
      case 'fog':
        weatherIcon = Icons.cloud;
        break;
      default:
        weatherIcon = Icons.wb_sunny;
    }

    return Card(
      elevation: 1,
      margin: const EdgeInsets.only(top: 8.0, bottom: 16.0),
      child: Padding(
        padding: const EdgeInsets.all(12.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(weatherIcon, size: 36, color: Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        noteContext.weatherDescription ?? noteContext.weatherCondition ?? 'Pogoda nieznana',
                        style: Theme.of(context).textTheme.titleMedium,
                      ),
                      if (noteContext.temperature != null)
                        Text(
                          '${noteContext.temperature!.toStringAsFixed(1)}°C',
                          style: Theme.of(context).textTheme.titleLarge?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildWeatherDetail(
                  context,
                  Icons.water_outlined,
                  'Wilgotność',
                  noteContext.humidity != null ? '${noteContext.humidity!.toStringAsFixed(0)}%' : 'N/A',
                ),
                _buildWeatherDetail(
                  context,
                  Icons.speed,
                  'Ciśnienie',
                  noteContext.pressure != null ? '${noteContext.pressure!.toStringAsFixed(0)} hPa' : 'N/A',
                ),
                _buildWeatherDetail(
                  context,
                  Icons.air,
                  'Wiatr',
                  noteContext.windSpeed != null ? '${noteContext.windSpeed!.toStringAsFixed(1)} m/s' : 'N/A',
                ),
              ],
            ),
            if (noteContext.precipitation != null && noteContext.precipitation! > 0)
              Padding(
                padding: const EdgeInsets.only(top: 8.0),
                child: _buildWeatherDetail(
                  context,
                  Icons.umbrella,
                  'Opady',
                  '${noteContext.precipitation!.toStringAsFixed(1)} mm',
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildWeatherDetail(BuildContext context, IconData icon, String label, String value) {
    return Column(
      children: [
        Icon(icon, size: 24, color: Theme.of(context).colorScheme.primary),
        const SizedBox(height: 4),
        Text(label, style: TextStyle(fontSize: 12, color: Theme.of(context).colorScheme.onSurfaceVariant)),
        Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
      ],
    );
  }

  String _formatFullDateTime(DateTime dateTime) {
    final now = DateTime.now();
    final difference = now.difference(dateTime);
    String ago;
    if (difference.inDays > 0) {
      ago = '${difference.inDays} day${difference.inDays > 1 ? 's' : ''} ago';
    } else if (difference.inHours > 0) {
      ago = '${difference.inHours} hour${difference.inHours > 1 ? 's' : ''} ago';
    } else if (difference.inMinutes > 0) {
      ago = '${difference.inMinutes} minute${difference.inMinutes > 1 ? 's' : ''} ago';
    } else {
      ago = 'Just now';
    }
    final formatted = '${dateTime.day.toString().padLeft(2, '0')}-${dateTime.month.toString().padLeft(2, '0')}-${dateTime.year} '
        '${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
    return '$formatted ($ago)';
  }
} 
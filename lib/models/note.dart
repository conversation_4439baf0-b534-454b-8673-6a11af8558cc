class Note {
  final int? id;
  final String title;
  final String content;
  final String type; // 'text', 'audio', 'mixed'
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? syncedAt;
  final bool isSynced;
  final String? serverId;
  final DateTime? deletedAt;
  final NoteContext? context;

  Note({
    this.id,
    required this.title,
    required this.content,
    this.type = 'text',
    DateTime? createdAt,
    DateTime? updatedAt,
    this.syncedAt,
    this.isSynced = false,
    this.serverId,
    this.deletedAt,
    this.context,
  }) : 
    createdAt = createdAt ?? DateTime.now(),
    updatedAt = updatedAt ?? DateTime.now();

  Note copyWith({
    int? id,
    String? title,
    String? content,
    String? type,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? syncedAt,
    bool? isSynced,
    String? serverId,
    DateTime? deletedAt,
    NoteContext? context,
  }) {
    return Note(
      id: id ?? this.id,
      title: title ?? this.title,
      content: content ?? this.content,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      syncedAt: syncedAt ?? this.syncedAt,
      isSynced: isSynced ?? this.isSynced,
      serverId: serverId ?? this.serverId,
      deletedAt: deletedAt ?? this.deletedAt,
      context: context ?? this.context,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'title': title,
      'content': content,
      'type': type,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'synced_at': syncedAt?.toIso8601String(),
      'is_synced': isSynced ? 1 : 0,
      'server_id': serverId,
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }

  factory Note.fromMap(Map<String, dynamic> map) {
    return Note(
      id: map['id'],
      title: map['title'] ?? '',
      content: map['content'] ?? '',
      type: map['type'] ?? 'text',
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
      syncedAt: map['synced_at'] != null ? DateTime.parse(map['synced_at']) : null,
      isSynced: map['is_synced'] == 1,
      serverId: map['server_id'],
      deletedAt: map['deleted_at'] != null ? DateTime.parse(map['deleted_at']) : null,
    );
  }
}

class NoteContext {
  final int? id;
  final int? noteId;
  final double? latitude;
  final double? longitude;
  final String? address;
  final String? weatherCondition;
  final String? weatherDescription;
  final double? temperature;
  final double? humidity;
  final double? pressure;
  final double? precipitation;
  final double? windSpeed;
  final double? noiseLevel;
  final double? lightLevel;
  final String? activityType;
  final double? movementSpeed;
  final int? batteryLevel;
  final String? connectionType;
  final String? timezone;
  final DateTime createdAt;

  NoteContext({
    this.id,
    this.noteId,
    this.latitude,
    this.longitude,
    this.address,
    this.weatherCondition,
    this.weatherDescription,
    this.temperature,
    this.humidity,
    this.pressure,
    this.precipitation,
    this.windSpeed,
    this.noiseLevel,
    this.lightLevel,
    this.activityType,
    this.movementSpeed,
    this.batteryLevel,
    this.connectionType,
    this.timezone,
    DateTime? createdAt,
  }) : createdAt = createdAt ?? DateTime.now();

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'note_id': noteId,
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'weather_condition': weatherCondition,
      'weather_description': weatherDescription,
      'temperature': temperature,
      'humidity': humidity,
      'pressure': pressure,
      'precipitation': precipitation,
      'wind_speed': windSpeed,
      'noise_level': noiseLevel,
      'light_level': lightLevel,
      'activity_type': activityType,
      'movement_speed': movementSpeed,
      'battery_level': batteryLevel,
      'connection_type': connectionType,
      'timezone': timezone,
      'created_at': createdAt.toIso8601String(),
    };
  }

  factory NoteContext.fromMap(Map<String, dynamic> map) {
    // Bezpieczne parsowanie daty
    DateTime? parseDateTime(String? dateStr) {
      if (dateStr == null) return DateTime.now();
      try {
        return DateTime.parse(dateStr);
      } catch (e) {
        print('[DEBUG] Błąd parsowania daty: $e');
        return DateTime.now();
      }
    }

    return NoteContext(
      id: map['id'],
      noteId: map['note_id'],
      latitude: map['latitude'],
      longitude: map['longitude'],
      address: map['address'],
      weatherCondition: map['weather_condition'],
      weatherDescription: map['weather_description'],
      temperature: map['temperature'],
      humidity: map['humidity'],
      pressure: map['pressure'],
      precipitation: map['precipitation'],
      windSpeed: map['wind_speed'],
      noiseLevel: map['noise_level'],
      lightLevel: map['light_level'],
      activityType: map['activity_type'],
      movementSpeed: map['movement_speed'],
      batteryLevel: map['battery_level'],
      connectionType: map['connection_type'],
      timezone: map['timezone'],
      createdAt: parseDateTime(map['created_at']),
    );
      }

      @override
      String toString() {
    return 'NoteContext{id: $id, noteId: $noteId, latitude: $latitude, longitude: $longitude, address: $address, ' +
      'weatherCondition: $weatherCondition, weatherDescription: $weatherDescription, temperature: $temperature, ' +
      'humidity: $humidity, pressure: $pressure, precipitation: $precipitation, windSpeed: $windSpeed, ' +
      'batteryLevel: $batteryLevel, connectionType: $connectionType, timezone: $timezone}';
  }

  NoteContext copyWith({
    int? id,
    int? noteId,
    double? latitude,
    double? longitude,
    String? address,
    String? weatherCondition,
    String? weatherDescription,
    double? temperature,
    double? humidity,
    double? pressure,
    double? precipitation,
    double? windSpeed,
    double? noiseLevel,
    double? lightLevel,
    String? activityType,
    double? movementSpeed,
    int? batteryLevel,
    String? connectionType,
    String? timezone,
    DateTime? createdAt,
  }) {
    return NoteContext(
      id: id ?? this.id,
      noteId: noteId ?? this.noteId,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      address: address ?? this.address,
      weatherCondition: weatherCondition ?? this.weatherCondition,
      weatherDescription: weatherDescription ?? this.weatherDescription,
      temperature: temperature ?? this.temperature,
      humidity: humidity ?? this.humidity,
      pressure: pressure ?? this.pressure,
      precipitation: precipitation ?? this.precipitation,
      windSpeed: windSpeed ?? this.windSpeed,
      noiseLevel: noiseLevel ?? this.noiseLevel,
      lightLevel: lightLevel ?? this.lightLevel,
      activityType: activityType ?? this.activityType,
      movementSpeed: movementSpeed ?? this.movementSpeed,
      batteryLevel: batteryLevel ?? this.batteryLevel,
      connectionType: connectionType ?? this.connectionType,
      timezone: timezone ?? this.timezone,
      createdAt: createdAt ?? this.createdAt,
    );
  }
} 
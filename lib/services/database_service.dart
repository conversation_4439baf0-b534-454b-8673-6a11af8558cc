import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/note.dart';

class DatabaseService {
  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'contextual_notes.db');
    return await openDatabase(
      path,
      version: 3,
      onCreate: _onCreate,
      onUpgrade: _onUpgrade,
    );
  }

      Future<void> _onUpgrade(Database db, int oldVersion, int newVersion) async {
    print('[DEBUG] Aktualizacja bazy danych z wersji $oldVersion do $newVersion');

    // Pobierz wszystkie kolumny tabeli note_context
    var columns = await db.rawQuery('PRAGMA table_info(note_context)');
    print('[DEBUG] Istniejące kolumny w note_context: ${columns.map((c) => c['name']).toList()}');

    if (oldVersion < 2) {
      // Dodaj kolumnę weather_description, jeśli nie istnieje
      bool hasWeatherDescription = columns.any((column) => column['name'] == 'weather_description');
      if (!hasWeatherDescription) {
        await db.execute('ALTER TABLE note_context ADD COLUMN weather_description TEXT');
        print('[DEBUG] Dodano kolumnę weather_description do tabeli note_context');
      }
    }

    if (oldVersion < 3) {
      // Dodaj kolumny pressure, precipitation i wind_speed, jeśli nie istnieją
      bool hasPressure = columns.any((column) => column['name'] == 'pressure');
      if (!hasPressure) {
        await db.execute('ALTER TABLE note_context ADD COLUMN pressure REAL');
        print('[DEBUG] Dodano kolumnę pressure do tabeli note_context');
      }

      bool hasPrecipitation = columns.any((column) => column['name'] == 'precipitation');
      if (!hasPrecipitation) {
        await db.execute('ALTER TABLE note_context ADD COLUMN precipitation REAL');
        print('[DEBUG] Dodano kolumnę precipitation do tabeli note_context');
      }

      bool hasWindSpeed = columns.any((column) => column['name'] == 'wind_speed');
      if (!hasWindSpeed) {
        await db.execute('ALTER TABLE note_context ADD COLUMN wind_speed REAL');
        print('[DEBUG] Dodano kolumnę wind_speed do tabeli note_context');
      }
    }
      }

  Future<void> _onCreate(Database db, int version) async {
    // Create notes table
    await db.execute('''
      CREATE TABLE notes (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        content TEXT NOT NULL,
        type TEXT CHECK(type IN ('text', 'audio', 'mixed')) NOT NULL,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        synced_at DATETIME,
        is_synced BOOLEAN DEFAULT 0,
        server_id TEXT,
        deleted_at DATETIME
      )
    ''');

    // Create note_context table
    await db.execute('''
      CREATE TABLE note_context (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_id INTEGER REFERENCES notes(id),
        latitude REAL,
        longitude REAL,
        address TEXT,
        weather_condition TEXT,
            weather_description TEXT,
            temperature REAL,
            humidity REAL,
            pressure REAL,
            precipitation REAL,
            wind_speed REAL,
        noise_level REAL,
        light_level REAL,
        activity_type TEXT,
        movement_speed REAL,
        battery_level INTEGER,
        connection_type TEXT,
        timezone TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Create audio_files table
    await db.execute('''
      CREATE TABLE audio_files (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_id INTEGER REFERENCES notes(id),
        file_path TEXT NOT NULL,
        duration INTEGER,
        transcription TEXT,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    ''');

    // Create sync_conflicts table
    await db.execute('''
      CREATE TABLE sync_conflicts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        note_id INTEGER REFERENCES notes(id),
        local_version TEXT,
        server_version TEXT,
        resolved BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    ''');
  }

  // Note operations
  Future<int> insertNote(Note note) async {
    final db = await database;
    return await db.insert('notes', note.toMap());
  }

  Future<List<Note>> getAllNotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'deleted_at IS NULL',
      orderBy: 'updated_at DESC',
    );

    List<Note> notes = [];
    for (final map in maps) {
      final note = Note.fromMap(map);
      final context = await getNoteContext(note.id!);
      notes.add(note.copyWith(context: context));
    }
    return notes;
  }

  Future<Note?> getNoteById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'id = ? AND deleted_at IS NULL',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      final note = Note.fromMap(maps.first);
      final context = await getNoteContext(note.id!);
      return note.copyWith(context: context);
    }
    return null;
  }

  Future<int> updateNote(Note note) async {
    final db = await database;
    return await db.update(
      'notes',
      note.copyWith(updatedAt: DateTime.now()).toMap(),
      where: 'id = ?',
      whereArgs: [note.id],
    );
  }

  Future<int> deleteNote(int id) async {
    final db = await database;
    return await db.update(
      'notes',
      {'deleted_at': DateTime.now().toIso8601String()},
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Context operations
  Future<int> insertNoteContext(NoteContext context) async {
    final db = await database;
    try {
      final contextMap = context.toMap();
      print('[DEBUG] Zapisywanie kontekstu do bazy danych: $contextMap');
      final id = await db.insert('note_context', contextMap);
      print('[DEBUG] Kontekst zapisany z ID: $id');
      return id;
    } catch (e) {
      print('[DEBUG] Błąd podczas zapisywania kontekstu: $e');
      rethrow;
    }
  }

  Future<NoteContext?> getNoteContext(int noteId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'note_context',
      where: 'note_id = ?',
      whereArgs: [noteId],
      orderBy: 'created_at DESC',
      limit: 1,
    );

    if (maps.isNotEmpty) {
      return NoteContext.fromMap(maps.first);
    }
    return null;
  }

  Future<int> updateNoteContext(NoteContext context) async {
    final db = await database;
    return await db.update(
      'note_context',
      context.toMap(),
      where: 'id = ?',
      whereArgs: [context.id],
    );
  }

  // Sync operations
  Future<List<Note>> getUnsyncedNotes() async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'notes',
      where: 'is_synced = 0 AND deleted_at IS NULL',
    );

    return List.generate(maps.length, (i) {
      return Note.fromMap(maps[i]);
    });
  }

  Future<int> markNoteAsSynced(int noteId, String serverId) async {
    final db = await database;
    return await db.update(
      'notes',
      {
        'is_synced': 1,
        'server_id': serverId,
        'synced_at': DateTime.now().toIso8601String(),
      },
      where: 'id = ?',
      whereArgs: [noteId],
    );
  }

  // Reset database schema - use with caution, only for debugging!
  Future<void> resetDatabaseSchema() async {
    final db = await database;

    // Dropuj istniejące tabele
    await db.execute('DROP TABLE IF EXISTS note_context');
    await db.execute('DROP TABLE IF EXISTS audio_files');
    await db.execute('DROP TABLE IF EXISTS sync_conflicts');
    await db.execute('DROP TABLE IF EXISTS notes');

    print('[DEBUG] Usunięto wszystkie tabele');

    // Odtwórz schemat bazy danych
    await _onCreate(db, 3);
    print('[DEBUG] Odtworzono schemat bazy danych');

    return;
  }

  // Close database
  Future<void> close() async {
    final db = await database;
    await db.close();
  }
} 
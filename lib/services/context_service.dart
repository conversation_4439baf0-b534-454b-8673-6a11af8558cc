import 'package:geolocator/geolocator.dart';
import 'package:geocoding/geocoding.dart';
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:battery_plus/battery_plus.dart';
import '../models/note.dart';
import 'package:flutter/material.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:http/http.dart' as http;
import 'dart:convert';

class ContextSummaryItem {
  final IconData icon;
  final String text;
  final VoidCallback? onTap;
  ContextSummaryItem({required this.icon, required this.text, this.onTap});
}

class ContextService {
  final Battery _battery = Battery();
  
  Future<NoteContext> collectContext() async {
    try {
      print('[DEBUG] Rozpoczęcie zbierania kontekstu');
      // Get location
      final position = await _getCurrentLocation();
      print('[DEBUG] Pobrano lokalizację: ${position != null ? '${position.latitude}, ${position.longitude}' : 'brak lokalizacji'}');

      // Get address from coordinates
      String? address;
      if (position != null) {
        address = await _getAddressFromCoordinates(position.latitude, position.longitude);
        print('[DEBUG] Pobrano adres: $address');
      }

      // Get weather data (zintegrowane z API pogodowym)
      final weatherData = await _getWeatherData(position?.latitude, position?.longitude);
      print('[DEBUG] Pobrano dane pogodowe: ${weatherData.toString()}');

      // Get device information
      final batteryLevel = await _battery.batteryLevel;
      print('[DEBUG] Poziom baterii: $batteryLevel%');
      final connectivityResults = await Connectivity().checkConnectivity();
      ConnectivityResult mainResult = (connectivityResults.isNotEmpty)
          ? connectivityResults.first
          : ConnectivityResult.none;
      final connectionType = _getConnectionTypeString(mainResult);
      print('[DEBUG] Typ połączenia: $connectionType');

      // Utwórz podstawowy kontekst z domyślnymi wartościami
      final context = NoteContext(
        latitude: position?.latitude,
        longitude: position?.longitude,
        address: address,
        weatherCondition: weatherData['condition'] as String?,
        weatherDescription: weatherData['description'] as String?,
        temperature: weatherData['temperature'] as double?,
        humidity: weatherData['humidity'] as double?,
        pressure: weatherData['pressure'] as double?,
        precipitation: weatherData['precipitation'] as double?,
        windSpeed: weatherData['windSpeed'] as double?,
        batteryLevel: batteryLevel,
        connectionType: connectionType,
        timezone: DateTime.now().timeZoneName,
        // Placeholder values for sensors not yet implemented
        noiseLevel: null,
        lightLevel: null,
        activityType: null,
        movementSpeed: null,
      );

      print('[DEBUG] Utworzono kontekst: ${context.toString()}');
      return context;
    } catch (e) {
      // Return minimal context if collection fails
      return NoteContext(
        timezone: DateTime.now().timeZoneName,
        batteryLevel: await _battery.batteryLevel,
      );
    }
  }

  Future<Position?> _getCurrentLocation() async {
    bool serviceEnabled = await Geolocator.isLocationServiceEnabled();
    if (!serviceEnabled) {
      return null;
    }

    LocationPermission permission = await Geolocator.checkPermission();
    if (permission == LocationPermission.denied) {
      permission = await Geolocator.requestPermission();
      if (permission == LocationPermission.denied) {
        return null;
      }
    }

    if (permission == LocationPermission.deniedForever) {
      return null;
    }

    try {
      return await Geolocator.getCurrentPosition(
        locationSettings: const LocationSettings(
          accuracy: LocationAccuracy.high,
          timeLimit: Duration(seconds: 10)
        )
      );
    } catch (e) {
      return null;
    }
  }

  Future<String?> _getAddressFromCoordinates(double latitude, double longitude) async {
    try {
      List<Placemark> placemarks = await placemarkFromCoordinates(latitude, longitude)
          .timeout(const Duration(seconds: 3));
      if (placemarks.isNotEmpty) {
        Placemark place = placemarks[0];
        final address = '${place.street}, ${place.locality}, ${place.country}';
        print('[DEBUG] Geocoded address: $address');
        return address;
      }
    } catch (e) {
      print('[DEBUG] Geocoding failed: $e');
      // Return null if geocoding fails
    }
    return null;
  }

  Future<Map<String, dynamic>> _getWeatherData(double? latitude, double? longitude) async {
    // Jeśli nie mamy danych lokalizacyjnych, zwracamy domyślne wartości
    if (latitude == null || longitude == null) {
      print('[DEBUG] Brak danych lokalizacyjnych dla pogody, zwracam domyślne wartości');
      return {
        'condition': 'Nieznane',
        'description': 'Brak danych',
        'temperature': null,
        'humidity': null,
        'pressure': null,
        'precipitation': null,
        'windSpeed': null,
      };
    }

    try {
      // Używamy API Open-Meteo do pobrania danych pogodowych
      // Nie wymaga klucza API dla użytku niekomercyjnego
      final url = 'https://api.open-meteo.com/v1/forecast?latitude=$latitude&longitude=$longitude'
          '&current=temperature_2m,relative_humidity_2m,precipitation,weather_code,pressure_msl,wind_speed_10m'
          '&hourly=precipitation&timezone=auto';

      print('[DEBUG] Wysyłanie zapytania do API pogodowego: $url');
      final response = await http.get(Uri.parse(url));

      if (response.statusCode == 200) {
        print('[DEBUG] Otrzymano odpowiedź z API pogodowego');
        final data = json.decode(response.body);
        final current = data['current'];
        print('[DEBUG] Dane pogodowe: $current');

        // Bezpieczne parsowanie wartości liczbowych
        double? temperature, humidity, pressure, precipitation, windSpeed;
        try {
          if (current['temperature_2m'] != null) {
            temperature = double.parse(current['temperature_2m'].toString());
          }
          if (current['relative_humidity_2m'] != null) {
            humidity = double.parse(current['relative_humidity_2m'].toString());
          }
          if (current['pressure_msl'] != null) {
            pressure = double.parse(current['pressure_msl'].toString());
          }
          if (current['precipitation'] != null) {
            precipitation = double.parse(current['precipitation'].toString());
          }
          if (current['wind_speed_10m'] != null) {
            windSpeed = double.parse(current['wind_speed_10m'].toString());
          }
        } catch (e) {
          print('[DEBUG] Błąd podczas parsowania wartości pogodowych: $e');
        }

        // Mapowanie kodów pogodowych WMO na nazwy warunków pogodowych
        int weatherCode = 0;
        try {
          if (current['weather_code'] != null) {
            weatherCode = int.parse(current['weather_code'].toString());
          }
        } catch (e) {
          print('[DEBUG] Błąd podczas parsowania kodu pogodowego: $e');
        }

        String condition = _getWeatherConditionFromWMO(weatherCode);
        String description = _getWeatherDescriptionFromWMO(weatherCode);

        return {
          'condition': condition,
          'description': description,
          'temperature': temperature,
          'humidity': humidity,
          'pressure': pressure,
          'precipitation': precipitation,
          'windSpeed': windSpeed,
        };
      } else {
        print('[DEBUG] Błąd API pogodowego: ${response.statusCode}, treść: ${response.body}');
        return {
          'condition': 'Błąd API',
          'description': 'Błąd pobierania danych',
          'temperature': null,
          'humidity': null,
          'pressure': null,
          'precipitation': null,
          'windSpeed': null,
        };
      }
    } catch (e) {
      print('[DEBUG] Wyjątek podczas pobierania pogody: $e');
      // W przypadku błędu zwracamy domyślne wartości
      return {
        'condition': 'Błąd',
        'description': 'Błąd połączenia',
        'temperature': null,
        'humidity': null,
        'pressure': null,
        'precipitation': null,
        'windSpeed': null,
      };
    }
  }

  String _getConnectionTypeString(ConnectivityResult result) {
    switch (result) {
      case ConnectivityResult.wifi:
        return 'WiFi';
      case ConnectivityResult.mobile:
        return 'Mobile';
      case ConnectivityResult.ethernet:
        return 'Ethernet';
      case ConnectivityResult.bluetooth:
        return 'Bluetooth';
      case ConnectivityResult.vpn:
        return 'VPN';
      case ConnectivityResult.other:
        return 'Other';
      case ConnectivityResult.none:
        return 'None';
    }
  }

  // Funkcja do mapowania kodów pogodowych WMO na nazwy warunków pogodowych
  String _getWeatherConditionFromWMO(int code) {
    if (code == 0) return 'Clear';
    if (code == 1) return 'Clear';
    if (code == 2) return 'Partly Cloudy';
    if (code == 3) return 'Cloudy';
    if (code == 45 || code == 48) return 'Fog';
    if (code >= 51 && code <= 55) return 'Drizzle';
    if (code >= 56 && code <= 57) return 'Freezing Drizzle';
    if (code >= 61 && code <= 65) return 'Rain';
    if (code >= 66 && code <= 67) return 'Freezing Rain';
    if (code >= 71 && code <= 77) return 'Snow';
    if (code >= 80 && code <= 82) return 'Rain';
    if (code >= 85 && code <= 86) return 'Snow';
    if (code >= 95 && code <= 99) return 'Thunderstorm';
    return 'Unknown';
  }

  // Funkcja do mapowania kodów pogodowych WMO na opisy warunków pogodowych
  String _getWeatherDescriptionFromWMO(int code) {
    if (code == 0) return 'Czyste niebo';
    if (code == 1) return 'Przeważnie czyste niebo';
    if (code == 2) return 'Częściowe zachmurzenie';
    if (code == 3) return 'Pochmurno';
    if (code == 45) return 'Mgła';
    if (code == 48) return 'Osadzająca się mgła';
    if (code == 51) return 'Lekka mżawka';
    if (code == 53) return 'Umiarkowana mżawka';
    if (code == 55) return 'Intensywna mżawka';
    if (code == 56) return 'Lekka marznąca mżawka';
    if (code == 57) return 'Intensywna marznąca mżawka';
    if (code == 61) return 'Lekki deszcz';
    if (code == 63) return 'Umiarkowany deszcz';
    if (code == 65) return 'Intensywny deszcz';
    if (code == 66) return 'Lekki marznący deszcz';
    if (code == 67) return 'Intensywny marznący deszcz';
    if (code == 71) return 'Lekkie opady śniegu';
    if (code == 73) return 'Umiarkowane opady śniegu';
    if (code == 75) return 'Intensywne opady śniegu';
    if (code == 77) return 'Ziarna śniegu';
    if (code == 80) return 'Lekkie przelotne opady';
    if (code == 81) return 'Umiarkowane przelotne opady';
    if (code == 82) return 'Intensywne przelotne opady';
    if (code == 85) return 'Lekkie opady śniegu';
    if (code == 86) return 'Intensywne opady śniegu';
    if (code == 95) return 'Burza';
    if (code == 96) return 'Burza z lekkim gradem';
    if (code == 99) return 'Burza z intensywnym gradem';
    return 'Nieznane warunki pogodowe';
  }

  String? getMapsUrl(double? latitude, double? longitude) {
    if (latitude == null || longitude == null) return null;
    return 'https://www.google.com/maps/search/?api=1&query=${latitude},$longitude';
  }

  List<ContextSummaryItem> getContextSummaryItems(NoteContext? context, BuildContext buildContext) {
    if (context == null) return [
      ContextSummaryItem(icon: Icons.info_outline, text: 'No context data'),
    ];
    List<ContextSummaryItem> items = [];
    if (context.address != null && context.address!.isNotEmpty) {
      items.add(ContextSummaryItem(
        icon: Icons.location_on,
        text: context.address!,
      ));
    }
    if (context.latitude != null && context.longitude != null) {
      final url = getMapsUrl(context.latitude, context.longitude);
      items.add(ContextSummaryItem(
        icon: Icons.public, // Changed from location pin to globe for coordinates
        text: '${context.latitude!.toStringAsFixed(5)}, ${context.longitude!.toStringAsFixed(5)}',
        onTap: url != null ? () async {
          if (await canLaunchUrl(Uri.parse(url))) {
            await launchUrl(Uri.parse(url), mode: LaunchMode.externalApplication);
          } else {
            ScaffoldMessenger.of(buildContext).showSnackBar(
              const SnackBar(content: Text('Could not open map.')),
            );
          }
        } : null,
      ));
    }
          if (context.weatherCondition != null) {
      IconData weatherIcon;
      // Ustaw odpowiednią ikonę na podstawie warunków pogodowych
      switch (context.weatherCondition?.toLowerCase()) {
        case 'clear':
          weatherIcon = Icons.wb_sunny;
          break;
        case 'partly cloudy':
          weatherIcon = Icons.cloud_queue;
          break;
        case 'cloudy':
          weatherIcon = Icons.cloud;
          break;
        case 'rain':
        case 'drizzle':
          weatherIcon = Icons.water_drop;
          break;
        case 'freezing drizzle':
        case 'freezing rain':
          weatherIcon = Icons.ac_unit;
          break;
        case 'thunderstorm':
          weatherIcon = Icons.flash_on;
          break;
        case 'snow':
          weatherIcon = Icons.ac_unit;
          break;
        case 'mist':
        case 'fog':
          weatherIcon = Icons.cloud;
          break;
        default:
          weatherIcon = Icons.wb_sunny;
      }

      // Podstawowe informacje o pogodzie
      items.add(ContextSummaryItem(
        icon: weatherIcon,
        text: '${context.weatherDescription ?? context.weatherCondition}, ' +
            (context.temperature != null ? '${context.temperature!.toStringAsFixed(1)}°C' : 'Temperatura nieznana'),
      ));

      // Informacje o wietrze
      if (context.windSpeed != null) {
        items.add(ContextSummaryItem(
          icon: Icons.air,
          text: 'Wiatr: ${context.windSpeed!.toStringAsFixed(1)} m/s',
        ));
      }

      // Informacje o wilgotności
      if (context.humidity != null) {
        items.add(ContextSummaryItem(
          icon: Icons.water_outlined,
          text: 'Wilgotność: ${context.humidity!.toStringAsFixed(0)}%',
        ));
      }

      // Informacje o ciśnieniu
      if (context.pressure != null) {
        items.add(ContextSummaryItem(
          icon: Icons.speed,
          text: 'Ciśnienie: ${context.pressure!.toStringAsFixed(0)} hPa',
        ));
      }

      // Informacje o opadach
      if (context.precipitation != null && context.precipitation! > 0) {
        items.add(ContextSummaryItem(
          icon: Icons.umbrella,
          text: 'Opady: ${context.precipitation!.toStringAsFixed(1)} mm',
        ));
      }
    }
    if (context.batteryLevel != null) {
      items.add(ContextSummaryItem(
        icon: Icons.battery_full,
        text: '${context.batteryLevel}%',
      ));
    }
    if (context.connectionType != null) {
      items.add(ContextSummaryItem(
        icon: Icons.wifi,
        text: context.connectionType!,
      ));
    }
    return items.isEmpty
        ? [ContextSummaryItem(icon: Icons.info_outline, text: 'No context data')]
        : items;
  }
} 
import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'screens/notes_list_screen.dart';
import 'providers/note_provider.dart';
import 'services/database_service.dart';
import 'package:path/path.dart';
import 'package:sqflite/sqflite.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Opcje debugowania bazy danych
  bool shouldDeleteDatabase = false;  // Usuń cały plik bazy danych
  bool resetSchema = false;    // Resetuj schemat bazy danych, ale zachowaj plik

  if (shouldDeleteDatabase) {
    final dbPath = await getDatabasesPath();
    final path = join(dbPath, 'contextual_notes.db');
    try {
      await deleteDatabase(path); // Funkcja z pakietu sqflite
      print('[DEBUG] Baza danych została usunięta');
    } catch (e) {
      print('[DEBUG] Błąd podczas usuwania bazy danych: $e');
    }
  } else if (resetSchema) {
    try {
      // Importuj dynamicznie, aby uniknąć cyklicznych zależności
      final dbService = DatabaseService();
      await dbService.resetDatabaseSchema();
      print('[DEBUG] Schemat bazy danych został zresetowany');
    } catch (e) {
      print('[DEBUG] Błąd podczas resetowania schematu bazy danych: $e');
    }
  }

  runApp(const ContextualNotesApp());
}

class ContextualNotesApp extends StatelessWidget {
  const ContextualNotesApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        ChangeNotifierProvider(create: (_) => NoteProvider()),
      ],
      child: MaterialApp(
        title: 'Contextual Notes',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          useMaterial3: true,
        ),
        home: const NotesListScreen(),
      ),
    );
  }
}

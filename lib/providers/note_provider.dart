import 'package:flutter/material.dart';
import '../models/note.dart';
import '../services/database_service.dart';
import '../services/context_service.dart';

class NoteProvider extends ChangeNotifier {
  final DatabaseService _databaseService = DatabaseService();
  final ContextService _contextService = ContextService();
  
  List<Note> _notes = [];
  bool _isLoading = false;
  String? _error;

  List<Note> get notes => _notes;
  bool get isLoading => _isLoading;
  String? get error => _error;

  NoteProvider() {
    _loadNotes();
  }

  Future<void> _loadNotes() async {
    _setLoading(true);
    try {
      _notes = await _databaseService.getAllNotes();
      _error = null;
    } catch (e) {
      _error = 'Failed to load notes: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> addNote(String title, String content, {String type = 'text'}) async {
    _setLoading(true);
    try {
      print('[DEBUG] Rozpoczęcie dodawania notatki: "$title"');

      // Collect context data
      print('[DEBUG] Zbieranie danych kontekstowych...');
      final context = await _contextService.collectContext();

      // Create note
      print('[DEBUG] Tworzenie obiektu notatki...');
      final note = Note(
        title: title,
        content: content,
        type: type,
        context: context,
      );

      // Save to database
      print('[DEBUG] Zapisywanie notatki do bazy danych...');
      final noteId = await _databaseService.insertNote(note);
      print('[DEBUG] Notatka zapisana z ID: $noteId');

      // Zawsze zapisujemy kontekst, nawet jeśli nie mamy danych lokalizacyjnych
      // aby mieć przynajmniej podstawowe informacje (poziom baterii, połączenie, strefa czasowa)
      print('[DEBUG] Zapisywanie danych kontekstowych z ID notatki...');
      try {
        final contextWithNoteId = context.copyWith(noteId: noteId);
        await _databaseService.insertNoteContext(contextWithNoteId);
        print('[DEBUG] Saved context: lat=${contextWithNoteId.latitude}, lon=${contextWithNoteId.longitude}, ' +
              'address=${contextWithNoteId.address}, battery=${contextWithNoteId.batteryLevel}%, ' +
              'connection=${contextWithNoteId.connectionType}');
      } catch (contextError) {
        print('[DEBUG] Błąd podczas zapisywania kontekstu: $contextError');
        // Kontynuuj, nawet jeśli zapisanie kontekstu się nie powiedzie
      }

      // Reload notes
      print('[DEBUG] Odświeżanie listy notatek...');
      await _loadNotes();
      _error = null;
    } catch (e, stackTrace) {
      print('[DEBUG] Błąd podczas dodawania notatki: $e');
      print('[DEBUG] Stack trace: $stackTrace');
      _error = 'Failed to add note: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> updateNote(Note note) async {
    _setLoading(true);
    try {
      await _databaseService.updateNote(note);
      await _loadNotes();
      _error = null;
    } catch (e) {
      _error = 'Failed to update note: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<void> deleteNote(int id) async {
    _setLoading(true);
    try {
      await _databaseService.deleteNote(id);
      await _loadNotes();
      _error = null;
    } catch (e) {
      _error = 'Failed to delete note: $e';
    } finally {
      _setLoading(false);
    }
  }

  Future<Note?> getNoteById(int id) async {
    try {
      return await _databaseService.getNoteById(id);
    } catch (e) {
      _error = 'Failed to get note: $e';
      return null;
    }
  }

  Future<List<Note>> getUnsyncedNotes() async {
    try {
      return await _databaseService.getUnsyncedNotes();
    } catch (e) {
      _error = 'Failed to get unsynced notes: $e';
      return [];
    }
  }

  List<ContextSummaryItem> getContextSummaryItems(NoteContext? context, BuildContext buildContext) {
    return _contextService.getContextSummaryItems(context, buildContext);
  }

  Future<NoteContext> collectContextForDebug() {
    return _contextService.collectContext();
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void clearError() {
    _error = null;
    notifyListeners();
  }

  @override
  void dispose() {
    _databaseService.close();
    super.dispose();
  }
} 
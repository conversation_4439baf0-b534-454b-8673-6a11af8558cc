# PRD i Specyfikacja Techniczna - Aplikacja Notatek Kontekstowych

## 1. Product Requirements Document (PRD)

### 1.1 Przegląd Produktu

**Nazwa produktu:** ContextNotes  
**Kategoria:** Produktywność / Notatki  
**Platforma:** iOS, Android  

**Opis produktu:**  
Zaawansowana aplikacja do tworzenia notatek, która automatycznie zapisuje kontekst sytuacyjny w momencie tworzenia notatki. Aplikacja umożliwia tworzenie notatek tekstowych oraz głosowych z automatycznym zapisem lokalizacji, pogody i innych danych kontekstowych.

### 1.2 Główne Funkcjonalności

#### Core Features:
- **Notatki tekstowe** - standardowe wpisywanie z klawiatury
- **Notatki głosowe** - nagrywanie i automatyczne tłumaczenie na tekst (Speech-to-Text)
- **Automatyczne zapisywanie kontekstu:**
  - Lokalizacja GPS (współrzędne, adres)
  - Aktualna pogoda
  - Data i czas
  - Dodatkowe dane kontekstowe

#### Dodatkowe Dane Kontekstowe (propozycje):
- **Dane środowiskowe:**
  - Poziom hałasu otoczenia
  - Poziom światła (brightness sensor)
  - Orientacja urządzenia
- **Dane behawioralne:**
  - Aktywność użytkownika (chodzenie, siedzenie, jazda)
  - Prędkość przemieszczania się
- **Dane czasowe:**
  - Strefa czasowa
  - Dzień tygodnia
  - Pora roku
- **Dane techniczne:**
  - Poziom baterii
  - Typ połączenia internetowego (WiFi/LTE/brak)
  - Używane aplikacje w tle

### 1.3 Funkcjonalności Synchronizacji

- **Praca offline** - pełna funkcjonalność bez połączenia internetowego
- **Synchronizacja automatyczna** - gdy połączenie jest dostępne
- **Conflict resolution** - obsługa konfliktów przy jednoczesnej edycji
- **Backup i restore** - tworzenie kopii zapasowych

### 1.4 User Stories

1. **Jako użytkownik chcę** szybko dodać notatkę tekstową, **aby** zapisać ważną informację
2. **Jako użytkownik chcę** nagrać notatkę głosową, **aby** nie musieć pisać podczas jazdy
3. **Jako użytkownik chcę** automatycznie zapisywać lokalizację, **aby** pamiętać gdzie miałem daną myśl
4. **Jako użytkownik chcę** wiedzieć jaka była pogoda, **aby** lepiej zapamiętać kontekst sytuacji
5. **Jako użytkownik chcę** pracować offline, **aby** aplikacja działała zawsze
6. **Jako użytkownik chcę** synchronizować dane między urządzeniami, **aby** mieć dostęp wszędzie

## 2. Specyfikacja Techniczna

### 2.1 Architektura Aplikacji

```
┌─────────────────────────────────────────────────────────────┐
│                        Frontend (Mobile)                    │
├─────────────────────────────────────────────────────────────┤
│  UI Layer (Flutter)                │
│  ├─ Screens (List, Create, Edit, Settings)                 │
│  ├─ Components (NoteCard, AudioRecorder, etc.)             │
│  └─ Navigation                                             │
├─────────────────────────────────────────────────────────────┤
│  Business Logic Layer                                       │
│  ├─ Note Management Service                                │
│  ├─ Audio Processing Service                               │
│  ├─ Context Collection Service                             │
│  └─ Synchronization Service                                │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                │
│  ├─ Local Database (SQLite/Realm)                         │
│  ├─ File Storage (Audio files, attachments)               │
│  └─ API Client                                            │
└─────────────────────────────────────────────────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────┐
│                        Backend (Optional)                   │
├─────────────────────────────────────────────────────────────┤
│  API Gateway (REST/GraphQL)                                │
├─────────────────────────────────────────────────────────────┤
│  Business Logic                                            │
│  ├─ User Management                                        │
│  ├─ Note Synchronization                                   │
│  ├─ Conflict Resolution                                    │
│  └─ External API Integration                               │
├─────────────────────────────────────────────────────────────┤
│  Data Layer                                                │
│  ├─ Database (PostgreSQL/MongoDB)                         │
│  ├─ File Storage (AWS S3/Google Cloud Storage)            │
│  └─ Cache (Redis)                                         │
└─────────────────────────────────────────────────────────────┘
```

### 2.2 Wybór Technologii

#### Frontend (Mobile):
**Rekomendacja: React Native**
- **Zalety:** Wspólny kod dla iOS/Android, bogaty ekosystem, szybki rozwój
- **Alternatywy:** Flutter (lepszy performance), Native (iOS Swift + Android Kotlin)

#### Backend:
**Rekomendacja: Node.js + Express/Fastify**
- **Alternatywy:** Python (Django/FastAPI), Go, Java (Spring Boot)

#### Baza Danych:
**Lokalna:** SQLite (wbudowana w mobile)
**Backend:** PostgreSQL (relacyjna) lub MongoDB (dokumentowa)

### 2.3 Struktura Bazy Danych

#### Lokalna Baza Danych (SQLite):

```sql
-- Tabela notatek
CREATE TABLE notes (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    title TEXT,
    content TEXT NOT NULL,
    type TEXT CHECK(type IN ('text', 'audio', 'mixed')) NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    synced_at DATETIME,
    is_synced BOOLEAN DEFAULT 0,
    server_id TEXT,
    deleted_at DATETIME
);

-- Tabela kontekstu notatek
CREATE TABLE note_context (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    note_id INTEGER REFERENCES notes(id),
    latitude REAL,
    longitude REAL,
    address TEXT,
    weather_condition TEXT,
    temperature REAL,
    humidity REAL,
    noise_level REAL,
    light_level REAL,
    activity_type TEXT,
    movement_speed REAL,
    battery_level INTEGER,
    connection_type TEXT,
    timezone TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabela plików audio
CREATE TABLE audio_files (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    note_id INTEGER REFERENCES notes(id),
    file_path TEXT NOT NULL,
    duration INTEGER,
    transcription TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Tabela konfliktów synchronizacji
CREATE TABLE sync_conflicts (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    note_id INTEGER REFERENCES notes(id),
    local_version TEXT,
    server_version TEXT,
    resolved BOOLEAN DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

### 2.4 API Endpoints

#### Notatki:
```
GET    /api/notes              # Lista notatek
POST   /api/notes              # Utworzenie notatki
GET    /api/notes/:id          # Szczegóły notatki
PUT    /api/notes/:id          # Aktualizacja notatki
DELETE /api/notes/:id          # Usunięcie notatki
```

#### Synchronizacja:
```
POST   /api/sync/notes         # Synchronizacja notatek
GET    /api/sync/conflicts     # Lista konfliktów
POST   /api/sync/resolve/:id   # Rozwiązanie konfliktu
```

#### Pliki:
```
POST   /api/upload/audio       # Upload pliku audio
GET    /api/files/:id          # Pobranie pliku
```

### 2.5 Serwisy Zewnętrzne

#### Wymagane Integracje:
1. **Serwis pogodowy:**
   - OpenWeatherMap API
   - AccuWeather API
   - Alternatywa: Weather.gov (US)

2. **Geocoding:**
   - Google Maps Geocoding API
   - OpenStreetMap Nominatim
   - MapBox Geocoding API

3. **Speech-to-Text:**
   - Google Cloud Speech-to-Text
   - Azure Speech Services
   - AWS Transcribe
   - Apple Speech Framework (iOS)
   - Android Speech Recognition (Android)

### 2.6 Permisje Aplikacji

#### iOS (Info.plist):
```xml
<key>NSLocationWhenInUseUsageDescription</key>
<string>Aplikacja potrzebuje dostępu do lokalizacji aby zapisać kontekst notatek</string>
<key>NSMicrophoneUsageDescription</key>
<string>Aplikacja potrzebuje dostępu do mikrofonu aby nagrywać notatki głosowe</string>
<key>NSSpeechRecognitionUsageDescription</key>
<string>Aplikacja potrzebuje dostępu do rozpoznawania mowy aby transkrybować notatki</string>
```

#### Android (AndroidManifest.xml):
```xml
<uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
<uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
<uses-permission android:name="android.permission.RECORD_AUDIO" />
<uses-permission android:name="android.permission.INTERNET" />
<uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
<uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
```

### 2.7 Strategia Offline-First

#### Synchronizacja:
1. **Kolejka operacji** - wszystkie zmiany są kolejkowane lokalnie
2. **Conflict Resolution** - strategia "last write wins" z możliwością manualnego rozwiązania
3. **Incremental Sync** - synchronizacja tylko zmienionych danych
4. **Background Sync** - synchronizacja w tle gdy aplikacja jest nieaktywna

#### Implementacja:
```javascript
// Przykład serwisu synchronizacji
class SyncService {
  async syncNotes() {
    const unsyncedNotes = await this.getUnsyncedNotes();
    const syncQueue = [];
    
    for (const note of unsyncedNotes) {
      syncQueue.push(this.syncNote(note));
    }
    
    await Promise.allSettled(syncQueue);
  }
  
  async syncNote(note) {
    try {
      if (note.serverId) {
        await this.updateNoteOnServer(note);
      } else {
        const serverId = await this.createNoteOnServer(note);
        await this.updateLocalNote(note.id, { serverId, synced: true });
      }
    } catch (error) {
      await this.handleSyncError(note, error);
    }
  }
}
```

## 3. Plan Implementacji

### 3.1 Faza 1 - MVP (6-8 tygodni)
- Podstawowy interfejs użytkownika
- Tworzenie notatek tekstowych
- Podstawowe zapisywanie kontekstu (lokalizacja, czas)
- Lokalna baza danych
- Podstawowa synchronizacja

### 3.2 Faza 2 - Rozszerzenie (4-6 tygodni)
- Notatki głosowe + Speech-to-Text
- Rozszerzone dane kontekstowe
- Zaawansowana synchronizacja
- Obsługa konfliktów

### 3.3 Faza 3 - Optymalizacja (2-4 tygodnie)
- Optymalizacja wydajności
- Zaawansowane funkcje UX
- Testy i debugging
- Przygotowanie do publikacji

## 4. Szacunkowe Koszty

### 4.1 Rozwój:
- **Frontend Developer (Mobile):** 2-3 miesiące
- **Backend Developer:** 1-2 miesiące
- **DevOps/Infrastructure:** 2-3 tygodnie
- **UI/UX Designer:** 3-4 tygodnie
- **QA Tester:** 2-3 tygodnie

### 4.2 Serwisy Zewnętrzne (miesięcznie):
- **Hosting:** $50-200
- **Baza danych:** $20-100
- **API pogodowe:** $0-50 (w zależności od użycia)
- **Speech-to-Text:** $10-100 (w zależności od użycia)
- **Maps API:** $0-50 (w zależności od użycia)

### 4.3 Publikacja:
- **App Store:** $99/rok
- **Google Play:** $25 jednorazowo

## 5. Metryki Sukcesu

### 5.1 Metryki Produktowe:
- Daily Active Users (DAU)
- Liczba tworzonych notatek dziennie
- Retention rate (1-day, 7-day, 30-day)
- Czas spędzony w aplikacji

### 5.2 Metryki Techniczne:
- Czas odpowiedzi aplikacji
- Sukces synchronizacji
- Crash rate
- Użycie baterii

### 5.3 Metryki Biznesowe:
- Conversion rate (free → paid)
- Customer Lifetime Value (CLV)
- Cost per acquisition (CPA)
- Monthly Recurring Revenue (MRR)

## 6. Ryzyka i Mitygacje

### 6.1 Ryzyka Techniczne:
- **Problem:** Słaba jakość Speech-to-Text
- **Mitygacja:** Integracja z wieloma dostawcami, możliwość manualnej korekty

- **Problem:** Problemy z synchronizacją
- **Mitygacja:** Robustny system kolejkowania, offline-first approach

### 6.2 Ryzyka Biznesowe:
- **Problem:** Konkurencja z dużymi graczami
- **Mitygacja:** Fokus na unikalnych funkcjach kontekstowych

- **Problem:** Problemy z prywatnością danych
- **Mitygacja:** Jasna polityka prywatności, minimalizacja zbieranych danych

## 7. Następne Kroki

1. **Walidacja koncepcji** - badania użytkowników, analiza konkurencji
2. **Stworzenie prototypu** - mockupy i wireframes
3. **Wybór stosu technologicznego** - finalna decyzja o technologiach
4. **Rozpoczęcie rozwoju** - implementacja MVP
5. **Testowanie** - testy funkcjonalne i użyteczności
6. **Soft launch** - publikacja dla ograniczonej grupy użytkowników
7. **Pełna publikacja** - launch w App Store i Google Play